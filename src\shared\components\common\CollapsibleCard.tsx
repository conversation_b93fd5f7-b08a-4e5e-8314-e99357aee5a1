import React, { useState, ReactNode } from 'react';

import { cn } from '@/shared/utils/cn';

import { Card, Icon } from './index';

export interface CollapsibleCardProps {
  /**
   * Tiêu đề của card
   */
  title: ReactNode;

  /**
   * Nội dung của card
   */
  children: ReactNode;

  /**
   * Class bổ sung cho card
   */
  className?: string;

  /**
   * Trạng thái mở ban đầu
   */
  defaultOpen?: boolean;

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component Card có thể đóng/mở
 */
const CollapsibleCard: React.FC<CollapsibleCardProps> = ({
  title,
  children,
  className,
  defaultOpen = true,
  onToggle,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const handleToggle = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    if (onToggle) {
      onToggle(newState);
    }
  };

  return (
    <Card className={cn('overflow-hidden transition-all duration-300', className)}>
      <div className="flex items-center justify-between p-1 cursor-pointer" onClick={handleToggle}>
        <div className="flex-1">{title}</div>
        <div className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
          <Icon
            name={isOpen ? 'chevron-up' : 'chevron-down'}
            className="text-gray-500 transition-transform duration-300"
          />
        </div>
      </div>

      <div
        className={cn(
          'transition-all duration-300 overflow-hidden',
          isOpen ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-4 pt-4 border-t border-gray-100 dark:border-gray-800">{children}</div>
      </div>
    </Card>
  );
};

export default CollapsibleCard;
